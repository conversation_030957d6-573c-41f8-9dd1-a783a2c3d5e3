import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, ArrowRight, ChevronLeft
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { useFormValidation } from '../../../../hooks/useBookingSubmission';
import { calculatePrice as calcPrice, PricingInput } from '../../../../lib/services/pricingService';

import { MoveOutBookingService } from '../../../../lib/api/moveOutBookingService';

interface FormData {
  propertySize: string;
  moveType: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
  howDidYouHear: string;
  newsletter: boolean;
  propertyType: string;
  bedrooms: string;
  bathrooms: string;
  frequency: string;
  cleaningType: string;
}

const BrandAlignedMoveOutForm: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const {
    errors: validationErrors
  } = useFormValidation('residential_move', user);

  const [formData, setFormData] = useState<FormData>({
    propertySize: 'medium',
    moveType: 'move-out',
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: '',
    howDidYouHear: '',
    newsletter: false,
    propertyType: 'house',
    bedrooms: '2',
    bathrooms: '2',
    frequency: 'one-time',
    cleaningType: 'standard'
  });



  const steps = [
    { id: 1, name: 'Property Details' },
    { id: 2, name: 'Service Options' },
    { id: 3, name: 'Add-Ons' },
    { id: 4, name: 'Contact' }
  ];



  const addOnOptions = [
    { id: 'appliances', name: 'Inside Appliances', price: 40, description: 'Oven, fridge, microwave deep clean' },
    { id: 'cabinets', name: 'Inside Cabinets', price: 60, description: 'All kitchen and bathroom cabinets' },
    { id: 'garage', name: 'Garage Cleaning', price: 80, description: 'Sweep and organize garage space' },
    { id: 'windows', name: 'Window Cleaning', price: 50, description: 'Interior and exterior windows' },
    { id: 'carpet', name: 'Carpet Deep Clean', price: 100, description: 'Professional carpet cleaning' },
    { id: 'pressure-wash', name: 'Exterior Pressure Wash', price: 120, description: 'Deck, patio, and walkways' }
  ];



  // Calculate total price
  const calculatePrice = () => {
    const pricingInput: PricingInput = {
      serviceType: 'residential_move',
      propertySize: ({ small: 800, medium: 1200, large: 1800, xlarge: 2500 })[formData.propertySize] || 1200,
      frequency: formData.frequency,
      addOns: formData.addOns,
      customOptions: {
        bedrooms: parseInt(formData.bedrooms) || 0,
        bathrooms: parseInt(formData.bathrooms) || 0,
        propertyType: formData.propertyType
      }
    };
    return Math.round(calcPrice(pricingInput).total);
  };

  // Validation functions
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      default:
        return '';
    }
  };

  // Step validation
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 1:
        return !!(formData.propertyType && formData.bedrooms && formData.bathrooms);
      case 2:
        return !!(formData.frequency && formData.cleaningType && formData.moveType);
      case 3:
        return true; // Add-ons are optional
      case 4: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode', 'preferredDate', 'preferredTime'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  const handleFieldChange = (field: string, value: string | string[] | boolean | number) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (validationErrors[field]) {
      // Note: clearFieldError would be used here if needed
    }
  };

  const handleSubmit = async () => {
    if (!isStepValid(4)) return;

    setIsSubmitting(true);

    try {
      if (!user) {
        // Save form data and redirect to login
        localStorage.setItem('moveOutCleaningFormData', JSON.stringify(formData));
        navigate('/auth/login', {
          state: { from: '/residential/moveout' }
        });
        return;
      }

      // Transform form data
      const moveOutFormData = {
        ...formData,
        moveType: (formData.moveType === 'both' ? 'move-out' : formData.moveType) as 'move-out' | 'move-in',
        bedrooms: parseInt(formData.bedrooms) || 0,
        bathrooms: parseInt(formData.bathrooms) || 0
      };

      // Create booking
      const savedBooking = await MoveOutBookingService.createMoveOutBooking(moveOutFormData, user);

      // Navigate to thank-you page
      navigate('/thank-you', { state: { formData: moveOutFormData, booking: savedBooking } });

    } catch (error) {
      console.error('Submission error:', error);
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit form');
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Property Details</h2>
              <p className="text-white/70">Tell us about your property</p>
            </div>
            <div className="space-y-6">
              <div>
                <label className="block text-white font-medium mb-4">Property Type</label>
                <div className="grid grid-cols-2 gap-4">
                  {['house', 'apartment', 'condo', 'townhouse'].map(type => (
                    <motion.div key={type} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className={`p-4 rounded-xl border-2 cursor-pointer ${formData.propertyType === type ? 'border-blue-400 bg-blue-500/20' : 'border-white/20 bg-white/5'}`} onClick={() => handleFieldChange('propertyType', type)}>
                      <h3 className="font-bold text-white text-center">{type.charAt(0).toUpperCase() + type.slice(1)}</h3>
                    </motion.div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-white font-medium mb-4">Bedrooms</label>
                <select value={formData.bedrooms} onChange={e => handleFieldChange('bedrooms', e.target.value)} className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                  <option value="">Select</option>
                  {['1', '2', '3', '4', '5+'].map(b => <option key={b} value={b}>{b}</option>)}
                </select>
              </div>
              <div>
                <label className="block text-white font-medium mb-4">Bathrooms</label>
                <select value={formData.bathrooms} onChange={e => handleFieldChange('bathrooms', e.target.value)} className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white">
                  <option value="">Select</option>
                  {['1', '1.5', '2', '2.5', '3', '3.5', '4+'].map(b => <option key={b} value={b}>{b}</option>)}
                </select>
              </div>
            </div>
          </motion.div>
        );
      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Service Options</h2>
              <p className="text-white/70">Choose your preferences</p>
            </div>
            <div className="space-y-6">
              <div>
                <label className="block text-white font-medium mb-4">Frequency</label>
                <div className="grid grid-cols-2 gap-4">
                  {['one-time', 'weekly', 'bi-weekly', 'monthly'].map(freq => (
                    <motion.div key={freq} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className={`p-4 rounded-xl border-2 cursor-pointer ${formData.frequency === freq ? 'border-blue-400 bg-blue-500/20' : 'border-white/20 bg-white/5'}`} onClick={() => handleFieldChange('frequency', freq)}>
                      <h3 className="font-bold text-white text-center">{freq.charAt(0).toUpperCase() + freq.slice(1)}</h3>
                    </motion.div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-white font-medium mb-4">Cleaning Type</label>
                <div className="grid grid-cols-2 gap-4">
                  {['standard', 'deep'].map(type => (
                    <motion.div key={type} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className={`p-4 rounded-xl border-2 cursor-pointer ${formData.cleaningType === type ? 'border-blue-400 bg-blue-500/20' : 'border-white/20 bg-white/5'}`} onClick={() => handleFieldChange('cleaningType', type)}>
                      <h3 className="font-bold text-white text-center">{type.charAt(0).toUpperCase() + type.slice(1)}</h3>
                    </motion.div>
                  ))}
                </div>
              </div>
              <div>
                <label className="block text-white font-medium mb-4">Move Type</label>
                <div className="grid grid-cols-3 gap-4">
                  {[
                    { id: 'move-in', label: 'Move In' },
                    { id: 'move-out', label: 'Move Out' },
                    { id: 'both', label: 'Both' }
                  ].map(type => (
                    <motion.div key={type.id} whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }} className={`p-4 rounded-xl border-2 cursor-pointer ${formData.moveType === type.id ? 'border-blue-400 bg-blue-500/20' : 'border-white/20 bg-white/5'}`} onClick={() => handleFieldChange('moveType', type.id)}>
                      <h3 className="font-bold text-white text-center">{type.label}</h3>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        );
      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Add-On Services</h2>
              <p className="text-white/70">Enhance your cleaning service</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {addOnOptions.map(addon => (
                <motion.div
                  key={addon.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 rounded-xl border-2 cursor-pointer ${
                    formData.addOns.includes(addon.id)
                      ? 'border-blue-400 bg-blue-500/20'
                      : 'border-white/20 bg-white/5'
                  }`}
                  onClick={() => {
                    const currentAddOns = formData.addOns || [];
                    if (currentAddOns.includes(addon.id)) {
                      handleFieldChange('addOns', currentAddOns.filter(id => id !== addon.id));
                    } else {
                      handleFieldChange('addOns', [...currentAddOns, addon.id]);
                    }
                  }}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-white">{addon.name}</h3>
                    <span className="text-blue-400 font-bold">+${addon.price}</span>
                  </div>
                  <p className="text-white/70 text-sm">{addon.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );
      case 4:
        return (
          <motion.div key="step4" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Contact Information</h2>
              <p className="text-white/70">Let us know how to reach you</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white font-medium mb-2">First Name</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={e => handleFieldChange('firstName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter first name"
                />
                {validationErrors.firstName && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Last Name</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={e => handleFieldChange('lastName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter last name"
                />
                {validationErrors.lastName && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={e => handleFieldChange('email', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter email address"
                />
                {validationErrors.email && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Phone</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={e => handleFieldChange('phone', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter phone number"
                />
                {validationErrors.phone && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                )}
              </div>
              <div className="md:col-span-2">
                <label className="block text-white font-medium mb-2">Address</label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={e => handleFieldChange('address', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter property address"
                />
                {validationErrors.address && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">City</label>
                <input
                  type="text"
                  value={formData.city}
                  onChange={e => handleFieldChange('city', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter city"
                />
                {validationErrors.city && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">ZIP Code</label>
                <input
                  type="text"
                  value={formData.zipCode}
                  onChange={e => handleFieldChange('zipCode', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter ZIP code"
                />
                {validationErrors.zipCode && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Preferred Date</label>
                <input
                  type="date"
                  value={formData.preferredDate}
                  onChange={e => handleFieldChange('preferredDate', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                />
                {validationErrors.preferredDate && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.preferredDate}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Preferred Time</label>
                <select
                  value={formData.preferredTime}
                  onChange={e => handleFieldChange('preferredTime', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                >
                  <option value="">Select time</option>
                  <option value="morning">Morning (8AM - 12PM)</option>
                  <option value="afternoon">Afternoon (12PM - 5PM)</option>
                  <option value="evening">Evening (5PM - 8PM)</option>
                </select>
                {validationErrors.preferredTime && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.preferredTime}</p>
                )}
              </div>
              <div className="md:col-span-2">
                <label className="block text-white font-medium mb-2">Special Instructions</label>
                <textarea
                  value={formData.specialInstructions}
                  onChange={e => handleFieldChange('specialInstructions', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Any special instructions or requests..."
                  rows={3}
                />
              </div>
            </div>
          </motion.div>
        );
    }
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      <AnimatedBackground>
        <div className="relative z-10 container mx-auto px-4 py-8">
          {/* Progress Bar */}
          <div className="mb-8">
            <div className="flex justify-between items-center mb-4">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`flex items-center ${index < steps.length - 1 ? 'flex-1' : ''}`}
                >
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center font-bold ${
                      currentStep >= step.id
                        ? 'bg-blue-500 text-white'
                        : 'bg-white/20 text-white/50'
                    }`}
                  >
                    {currentStep > step.id ? <CheckCircle className="w-6 h-6" /> : step.id}
                  </div>
                  <span className={`ml-2 font-medium ${
                    currentStep >= step.id ? 'text-white' : 'text-white/50'
                  }`}>
                    {step.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-1 mx-4 rounded-full ${
                      currentStep > step.id ? 'bg-blue-500' : 'bg-white/20'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Form Content */}
          <div className="max-w-4xl mx-auto">
            <AnimatePresence mode="wait">
              {renderStep()}
            </AnimatePresence>

            {/* Navigation & Price */}
            <div className="mt-12 flex justify-between items-center">
              <div>
                {currentStep > 1 && (
                  <Button
                    onClick={prevStep}
                    className="bg-white/10 hover:bg-white/20 text-white border border-white/20"
                  >
                    <ChevronLeft className="w-4 h-4 mr-2" />
                    Previous
                  </Button>
                )}
              </div>

              <div className="text-center">
                <div className="bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl p-4">
                  <p className="text-white/70 text-sm">Total Price</p>
                  <p className="text-2xl font-bold text-white">${calculatePrice()}</p>
                </div>
              </div>

              <div>
                {currentStep < steps.length ? (
                  <Button
                    onClick={nextStep}
                    disabled={!isStepValid(currentStep)}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting || !isStepValid(4)}
                    className="bg-green-500 hover:bg-green-600 text-white"
                  >
                    {isSubmitting ? 'Processing...' : 'Book Now'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>

            {submitError && (
              <div className="mt-4 p-4 bg-red-500/20 border border-red-400/30 rounded-lg">
                <p className="text-red-400">{submitError}</p>
              </div>
            )}
          </div>
        </div>
      </AnimatedBackground>
    </div>
  );
};

export default BrandAlignedMoveOutForm;