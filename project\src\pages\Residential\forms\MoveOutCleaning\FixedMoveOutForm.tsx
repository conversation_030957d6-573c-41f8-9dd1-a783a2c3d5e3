import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, ArrowRight, ChevronLeft, Home, Calendar, Clock,
  Users, Sparkles, Shield, Star
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';

// COMPLETELY FIXED MOVE-OUT FORM - NO COMPLEX LOGIC
const FixedMoveOutForm: React.FC = () => {
  console.log('🚀 FIXED MOVE-OUT FORM LOADED - TIMESTAMP:', new Date().toISOString());
  
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [formData, setFormData] = useState({
    propertyType: 'house',
    propertySize: 'medium',
    bedrooms: '3',
    bathrooms: '2',
    moveType: 'move-out',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    phone: '**********',
    address: '123 Main St',
    city: 'Anytown',
    zipCode: '12345',
    preferredDate: '2024-12-25',
    preferredTime: 'morning',
    // CRITICAL: These fields ensure correct service type detection
    serviceType: 'residential_move',
    cleaningType: 'move',
    frequency: 'one-time',
    addOns: [],
    specialInstructions: ''
  });

  const handleSubmit = () => {
    console.log('🚀 FIXED FORM SUBMIT - Form data:', formData);
    setShowPaymentModal(true);
  };

  const handlePaymentComplete = () => {
    console.log('🚀 PAYMENT COMPLETED');
    setShowPaymentModal(false);
    navigate('/accountdashboard');
  };

  return (
    <div className="min-h-screen">
      <AnimatedBackground>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <motion.h1 
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl md:text-5xl font-bold text-white mb-4"
              >
                🚀 FIXED Move-Out Cleaning Form
              </motion.h1>
              <motion.p 
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-xl text-white/80"
              >
                Pre-filled form for testing - just click "Test Booking"
              </motion.p>
            </div>

            {/* Pre-filled Form Display */}
            <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 mb-8">
              <h2 className="text-2xl font-bold text-white mb-6">Pre-filled Test Data</h2>
              <div className="grid grid-cols-2 gap-4 text-white">
                <div><strong>Property Type:</strong> {formData.propertyType}</div>
                <div><strong>Property Size:</strong> {formData.propertySize}</div>
                <div><strong>Bedrooms:</strong> {formData.bedrooms}</div>
                <div><strong>Bathrooms:</strong> {formData.bathrooms}</div>
                <div><strong>Move Type:</strong> {formData.moveType}</div>
                <div><strong>Service Type:</strong> {formData.serviceType}</div>
                <div><strong>Cleaning Type:</strong> {formData.cleaningType}</div>
                <div><strong>Name:</strong> {formData.firstName} {formData.lastName}</div>
                <div><strong>Email:</strong> {formData.email}</div>
                <div><strong>Phone:</strong> {formData.phone}</div>
                <div><strong>Address:</strong> {formData.address}</div>
                <div><strong>City:</strong> {formData.city}</div>
                <div><strong>ZIP:</strong> {formData.zipCode}</div>
                <div><strong>Date:</strong> {formData.preferredDate}</div>
                <div><strong>Time:</strong> {formData.preferredTime}</div>
              </div>
            </div>

            {/* Test Button */}
            <div className="text-center">
              <Button
                onClick={handleSubmit}
                className="bg-green-500 hover:bg-green-600 text-white text-xl px-8 py-4"
              >
                🚀 Test Booking (Pre-filled Data)
                <ArrowRight className="w-6 h-6 ml-2" />
              </Button>
            </div>

            {/* Instructions */}
            <div className="mt-8 bg-blue-500/20 border border-blue-400/30 rounded-lg p-6">
              <h3 className="text-white font-bold mb-4">🔧 Debug Instructions:</h3>
              <ul className="text-white/80 space-y-2">
                <li>1. Click "Test Booking" button above</li>
                <li>2. Watch console for debug messages</li>
                <li>3. PaymentOptionsModal should open</li>
                <li>4. Look for "serviceType: residential_move" in console</li>
                <li>5. Should NOT see "Property type is required" error</li>
              </ul>
            </div>
          </div>
        </div>
      </AnimatedBackground>

      {/* Payment Modal with FIXED data */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={299}
        description="Move-Out Cleaning Service (FIXED)"
        customerEmail={formData.email}
        formData={{
          // EXPLICITLY SET ALL REQUIRED FIELDS
          ...formData,
          serviceType: 'residential_move',
          cleaningType: 'move',
          moveType: 'move-out',
          totalPrice: 299,
          // Ensure property type is set
          propertyType: 'house',
          // Add debugging flags
          isTestForm: true,
          debugTimestamp: new Date().toISOString()
        }}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </div>
  );
};

export default FixedMoveOutForm;
