import React from 'react';

const TestMoveOutForm: React.FC = () => {
  console.log('🧪 TEST MOVE-OUT FORM LOADED - TIMESTAMP:', new Date().toISOString());

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
        <h1 className="text-3xl font-bold text-white text-center mb-4">
          Test Move-Out Form
        </h1>
        <p className="text-white/70 text-center mb-6">
          This is a minimal test form to verify routing is working.
        </p>
        <div className="space-y-4">
          <div className="p-4 bg-green-500/20 border border-green-400/30 rounded-lg">
            <p className="text-green-400 text-sm">✅ Component loaded successfully</p>
          </div>
          <div className="p-4 bg-blue-500/20 border border-blue-400/30 rounded-lg">
            <p className="text-blue-400 text-sm">🔧 Routing is working correctly</p>
          </div>
          <div className="p-4 bg-purple-500/20 border border-purple-400/30 rounded-lg">
            <p className="text-purple-400 text-sm">🚀 Ready to debug main form</p>
          </div>
        </div>
        <div className="mt-6 text-center">
          <button 
            className="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors"
            onClick={() => console.log('Test button clicked')}
          >
            Test Button
          </button>
        </div>
      </div>
    </div>
  );
};

export default TestMoveOutForm;
