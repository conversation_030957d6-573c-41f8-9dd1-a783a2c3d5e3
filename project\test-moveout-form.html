<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Move-Out Form</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Move-Out Form Testing</h1>
    
    <div class="test-section">
        <h2>Quick Tests</h2>
        <button class="test-button" onclick="openForm()">Open Move-Out Form</button>
        <button class="test-button" onclick="testFormData()">Test Form Data Structure</button>
        <button class="test-button" onclick="testServiceType()">Test Service Type Detection</button>
        <button class="test-button" onclick="checkConsole()">Check Console Logs</button>
    </div>

    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <div class="test-section">
        <h2>Instructions</h2>
        <ol>
            <li><strong>Open Move-Out Form</strong> - Opens the form in a new tab</li>
            <li><strong>Fill out the form completely:</strong>
                <ul>
                    <li>Step 1: Select House, 3 Bedroom, 3 bedrooms, 2 bathrooms</li>
                    <li>Step 2: Select Move-Out Cleaning</li>
                    <li>Step 3: Skip add-ons or select any</li>
                    <li>Step 4: Fill all contact fields with valid data</li>
                </ul>
            </li>
            <li><strong>Click "Book Now"</strong> and watch the browser console</li>
            <li><strong>Look for these debug messages:</strong>
                <ul>
                    <li>"=== MOVE-OUT FORM DEBUG ===" - Form data being sent</li>
                    <li>"=== PAYMENT MODAL DEBUG ===" - PaymentOptionsModal receiving data</li>
                    <li>"FORCED serviceType to residential_move" - Service type forced</li>
                    <li>"=== BOOKING SERVICE VALIDATION DEBUG ===" - BookingService validation</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="test-section">
        <h2>Expected Behavior</h2>
        <div class="info">
            <strong>✅ Success Case:</strong><br>
            - Form opens PaymentOptionsModal<br>
            - Console shows "serviceType: residential_move"<br>
            - BookingService validation passes<br>
            - No "Property type is required" error
        </div>
        <div class="error">
            <strong>❌ Failure Case:</strong><br>
            - Console shows "serviceType: residential_regular"<br>
            - BookingService validation fails<br>
            - Error: "Property type is required"
        </div>
    </div>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function openForm() {
            window.open('http://localhost:5173/residential/moveout', '_blank');
            addResult('✅ Opened move-out form in new tab', 'success');
        }

        function testFormData() {
            const testData = {
                propertyType: 'house',
                propertySize: 'medium',
                bedrooms: '3',
                bathrooms: '2',
                moveType: 'move-out',
                cleaningType: 'move',
                serviceType: 'residential_move'
            };
            
            addResult(`📊 Test Form Data Structure:<br><pre>${JSON.stringify(testData, null, 2)}</pre>`, 'info');
        }

        function testServiceType() {
            addResult(`🔍 Service Type Detection Logic:<br>
                - cleaningType: 'move' → should trigger residential_move<br>
                - moveType: 'move-out' → should trigger residential_move<br>
                - serviceType: 'residential_move' → should be preserved`, 'info');
        }

        function checkConsole() {
            addResult(`🔧 Console Debug Messages to Look For:<br>
                1. "=== MOVE-OUT FORM DEBUG ===" - Form submission<br>
                2. "=== PAYMENT MODAL DEBUG ===" - Modal data reception<br>
                3. "FORCED serviceType to residential_move" - Service type override<br>
                4. "=== BOOKING SERVICE VALIDATION DEBUG ===" - Validation process<br>
                <br>
                <strong>Open browser DevTools (F12) → Console tab</strong>`, 'info');
        }

        // Auto-run initial tests
        window.onload = function() {
            addResult('🚀 Move-Out Form Testing Interface Loaded', 'success');
            addResult('📝 Follow the instructions above to test the form', 'info');
        };
    </script>
</body>
</html>
