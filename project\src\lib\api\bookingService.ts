import { secureInsert } from '../database/secureQuery';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../utils/errorHandler';
import { FormValidator, InputSanitizer } from '../utils/validation';
import { supabase } from '../supabase/client';
import { User } from '@supabase/supabase-js';

export interface StandardizedBookingData {
  id?: string;
  user_id?: string | null;
  service_type: string;
  property_details: {
    propertyType?: string;
    propertySize?: string;
    squareFootage?: number;
    address?: string;
    city?: string;
    zipCode?: string;
    [key: string]: unknown;
  };
  service_details: {
    serviceType?: string;
    frequency?: string;
    addOns?: string[];
    specialRequests?: string[];
    [key: string]: unknown;
  };
  schedule: {
    preferredDate?: string;
    preferredTime?: string;
    timeSlot?: string;
    frequency?: string;
    [key: string]: unknown;
  };
  contact: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    companyName?: string;
    jobTitle?: string;
    [key: string]: string;
  };
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'rescheduled';
  payment_status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  total_price?: number;
  special_instructions?: string;
  metadata?: {
    submittedAt: string;
    requestType: 'booking' | 'estimate';
    formVersion?: string;
    source?: string;
    [key: string]: unknown;
  };
  created_at?: string;
  updated_at?: string;
}

export interface BookingValidationResult {
  isValid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  sanitizedData?: StandardizedBookingData;
}

/**
 * Standardized booking service with consistent validation and submission
 */
export class BookingService {
  /**
   * Validate and sanitize booking data
   */
  static validateBookingData(
    formData: any,
    serviceType: string,
    user: User | null
  ): BookingValidationResult {
    try {
      // Validate booking data
      console.log('=== BOOKING SERVICE VALIDATION DEBUG ===');
      console.log('BookingService.validateBookingData called for service type:', serviceType);
      console.log('Received formData:', formData);
      console.log('formData.propertyType:', formData.propertyType);
      console.log('formData.cleaningType:', formData.cleaningType);
      console.log('formData.moveType:', formData.moveType);
      console.log('User:', user?.id);
      console.log('========================================');
      
      const errors: Array<{ field: string; message: string; code: string }> = [];

      // Basic required field validation with support for both flat and nested structures
      const requiredFields = this.getRequiredFields(serviceType);

      for (const field of requiredFields) {
        const fieldValue = this.getFieldValue(formData, field);
        if (!fieldValue || (typeof fieldValue === 'string' && fieldValue.trim() === '')) {
          errors.push({
            field,
            message: `${this.getFieldDisplayName(field)} is required`,
            code: 'REQUIRED'
          });
        }
      }

      // Email validation - check multiple possible locations
      const email = this.getFieldValue(formData, 'email');
      
      if (email && !this.isValidEmail(email)) {
        errors.push({
          field: 'email',
          message: 'Please enter a valid email address',
          code: 'INVALID_EMAIL'
        });
      }

      // Phone validation - check multiple possible locations
      const phone = this.getFieldValue(formData, 'phone');
      if (phone && !this.isValidPhone(phone)) {
        errors.push({
          field: 'phone',
          message: 'Please enter a valid phone number',
          code: 'INVALID_PHONE'
        });
      }

      // Date validation - check multiple possible locations
      const preferredDate = this.getFieldValue(formData, 'preferredDate');
      if (preferredDate && !this.isValidDate(preferredDate)) {
        errors.push({
          field: 'preferredDate',
          message: 'Please enter a valid date',
          code: 'INVALID_DATE'
        });
      }

      // Sanitize the data
      const sanitizedData = this.sanitizeBookingData(formData, serviceType, user);

      return {
        isValid: errors.length === 0,
        errors,
        sanitizedData: errors.length === 0 ? sanitizedData : undefined
      };
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'VALIDATION_ERROR',
        'Failed to validate booking data',
        error
      ));

      return {
        isValid: false,
        errors: [{
          field: 'general',
          message: 'Validation failed due to system error',
          code: 'SYSTEM_ERROR'
        }]
      };
    }
  }

  /**
   * Sanitize booking data into standardized format
   */
  static sanitizeBookingData(
    formData: any,
    serviceType: string,
    user: User | null
  ): StandardizedBookingData {
    // Sanitize contact information - handle both flat and nested structures
    const contactData = formData.contact || formData;
    const contact = {
      firstName: InputSanitizer.sanitizeText(contactData.firstName || contactData.fullName?.split(' ')[0] || ''),
      lastName: InputSanitizer.sanitizeText(contactData.lastName || contactData.fullName?.split(' ').slice(1).join(' ') || ''),
      email: InputSanitizer.sanitizeEmail(contactData.email || ''),
      phone: InputSanitizer.sanitizePhone(contactData.phone || ''),
      ...(contactData.companyName && {
        companyName: InputSanitizer.sanitizeText(contactData.companyName)
      }),
      ...(contactData.jobTitle && {
        jobTitle: InputSanitizer.sanitizeText(contactData.jobTitle)
      })
    };

    // Sanitize property details
    const property_details = {
      ...(formData.propertyType && {
        propertyType: InputSanitizer.sanitizeText(formData.propertyType)
      }),
      ...(formData.propertySize && {
        propertySize: InputSanitizer.sanitizeText(formData.propertySize)
      }),
      ...(formData.squareFootage && {
        squareFootage: InputSanitizer.sanitizeNumber(formData.squareFootage)
      }),
      ...(formData.address && {
        address: InputSanitizer.sanitizeText(formData.address)
      }),
      ...(formData.city && {
        city: InputSanitizer.sanitizeText(formData.city)
      }),
      ...(formData.zipCode && {
        zipCode: InputSanitizer.sanitizeText(formData.zipCode)
      })
    };

    // Sanitize schedule
    const schedule = {
      ...(formData.preferredDate && {
        preferredDate: InputSanitizer.sanitizeText(formData.preferredDate)
      }),
      ...(formData.preferredTime && {
        preferredTime: InputSanitizer.sanitizeText(formData.preferredTime)
      }),
      ...(formData.timeSlot && {
        timeSlot: InputSanitizer.sanitizeText(formData.timeSlot)
      }),
      ...(formData.frequency && {
        frequency: InputSanitizer.sanitizeText(formData.frequency)
      })
    };

    // Sanitize service details based on service type
    const service_details = this.getServiceSpecificDetails(formData, serviceType);

    return {
      user_id: user?.id || null,
      service_type: serviceType,
      property_details,
      service_details,
      schedule,
      contact,
      status: 'pending',
      payment_status: 'pending',
      total_price: formData.totalPrice || 0,
      special_instructions: formData.specialInstructions ?
        InputSanitizer.sanitizeText(formData.specialInstructions) : undefined,
      metadata: {
        submittedAt: new Date().toISOString(),
        requestType: formData.requestType || 'booking',
        formVersion: '2.0',
        source: 'web_form'
      }
    };
  }

  /**
   * Get required fields for service type
   */
  static getRequiredFields(serviceType: string): string[] {
    const baseFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode'];

    switch (serviceType) {
      case 'residential_regular':
      case 'residential_deep':
      case 'residential_move':
        return [...baseFields, 'propertyType', 'propertySize', 'preferredDate', 'preferredTime'];

      case 'pool':
        return [...baseFields, 'propertyType', 'preferredDate', 'preferredTime'];

      case 'carpet':
        return [...baseFields, 'propertyType', 'preferredDate', 'preferredTime'];

      case 'office':
      case 'floor':
        return [...baseFields, 'propertyType', 'squareFootage', 'preferredDate', 'preferredTime'];

      case 'construction':
        return [...baseFields, 'projectType', 'squareFootage', 'preferredDate', 'preferredTime'];

      case 'waste-management':
        return [...baseFields, 'wasteType', 'preferredDate', 'preferredTime'];

      default:
        return baseFields;
    }
  }

  /**
   * Get field display name
   */
  static getFieldDisplayName(fieldName: string): string {
    const displayNames: Record<string, string> = {
      firstName: 'First name',
      lastName: 'Last name',
      email: 'Email address',
      phone: 'Phone number',
      address: 'Address',
      city: 'City',
      zipCode: 'ZIP code',
      propertyType: 'Property type',
      propertySize: 'Property size',
      squareFootage: 'Square footage',
      preferredDate: 'Preferred date',
      preferredTime: 'Preferred time',
      projectType: 'Project type',
      wasteType: 'Waste type'
    };

    return displayNames[fieldName] || fieldName.replace(/([A-Z])/g, ' $1').toLowerCase();
  }

  /**
   * Validate email format
   */
  static isValidEmail(email: string): boolean {
    if (!email || typeof email !== 'string') {
      return false;
    }
    
    const trimmedEmail = email.trim();
    if (!trimmedEmail) {
      return false;
    }
    
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(trimmedEmail);
  }

  /**
   * Validate phone format
   */
  static isValidPhone(phone: string): boolean {
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    const cleanPhone = phone.replace(/\D/g, '');
    return cleanPhone.length >= 10 && phoneRegex.test(cleanPhone);
  }

  /**
   * Validate date format and ensure it's not in the past
   */
  static isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return !isNaN(date.getTime()) && date >= today;
  }

  /**
   * Get service-specific details based on service type
   */
  static getServiceSpecificDetails(formData: any, serviceType: string): Record<string, unknown> {
    const baseDetails = {
      ...(formData.addOns && { addOns: formData.addOns }),
      ...(formData.specialRequests && { specialRequests: formData.specialRequests })
    };

    switch (serviceType) {
      case 'residential_regular':
      case 'residential_deep':
        return {
          ...baseDetails,
          ...(formData.bedrooms && { bedrooms: InputSanitizer.sanitizeNumber(formData.bedrooms) }),
          ...(formData.bathrooms && { bathrooms: InputSanitizer.sanitizeNumber(formData.bathrooms) }),
          ...(formData.frequency && { frequency: InputSanitizer.sanitizeText(formData.frequency) }),
          ...(formData.cleaningType && { cleaningType: InputSanitizer.sanitizeText(formData.cleaningType) })
        };

      case 'residential_move':
        return {
          ...baseDetails,
          // Move-out specific fields
          ...(formData.moveType && { moveType: InputSanitizer.sanitizeText(formData.moveType) }),
          ...(formData.propertySize && { propertySize: InputSanitizer.sanitizeText(formData.propertySize) }),
          ...(formData.depositGuarantee && { depositGuarantee: formData.depositGuarantee }),
          ...(formData.landlordRequirements && { landlordRequirements: InputSanitizer.sanitizeText(formData.landlordRequirements) }),
          // Standard residential fields for compatibility
          ...(formData.bedrooms && { bedrooms: InputSanitizer.sanitizeNumber(formData.bedrooms) }),
          ...(formData.bathrooms && { bathrooms: InputSanitizer.sanitizeNumber(formData.bathrooms) }),
          // Move-out service specifics
          ...(formData.totalPrice && { totalPrice: InputSanitizer.sanitizeNumber(formData.totalPrice) }),
          ...(formData.serviceDisplayName && { serviceDisplayName: InputSanitizer.sanitizeText(formData.serviceDisplayName) })
        };

      case 'pool':
        return {
          ...baseDetails,
          ...(formData.poolType && { poolType: InputSanitizer.sanitizeText(formData.poolType) }),
          ...(formData.poolSize && { poolSize: InputSanitizer.sanitizeText(formData.poolSize) }),
          ...(formData.poolCondition && { poolCondition: InputSanitizer.sanitizeText(formData.poolCondition) })
        };

      case 'carpet':
        return {
          ...baseDetails,
          ...(formData.carpetType && { carpetType: InputSanitizer.sanitizeText(formData.carpetType) }),
          ...(formData.stainTreatment && { stainTreatment: formData.stainTreatment }),
          ...(formData.roomCount && { roomCount: InputSanitizer.sanitizeNumber(formData.roomCount) })
        };

      case 'office':
      case 'floor':
        return {
          ...baseDetails,
          ...(formData.floorType && { floorType: InputSanitizer.sanitizeText(formData.floorType) }),
          ...(formData.officeType && { officeType: InputSanitizer.sanitizeText(formData.officeType) }),
          ...(formData.employeeCount && { employeeCount: InputSanitizer.sanitizeNumber(formData.employeeCount) })
        };

      case 'construction':
        return {
          ...baseDetails,
          ...(formData.constructionPhase && { constructionPhase: InputSanitizer.sanitizeText(formData.constructionPhase) }),
          ...(formData.projectType && { projectType: InputSanitizer.sanitizeText(formData.projectType) }),
          ...(formData.completionDate && { completionDate: InputSanitizer.sanitizeText(formData.completionDate) })
        };

      case 'waste-management':
        return {
          ...baseDetails,
          ...(formData.wasteType && { wasteType: InputSanitizer.sanitizeText(formData.wasteType) }),
          ...(formData.containerSize && { containerSize: InputSanitizer.sanitizeText(formData.containerSize) }),
          ...(formData.pickupFrequency && { pickupFrequency: InputSanitizer.sanitizeText(formData.pickupFrequency) })
        };

      default:
        return baseDetails;
    }
  }

  /**
   * Save booking to database with proper validation and error handling
   */
  static async saveBooking(
    formData: any,
    serviceType: string,
    user: User | null
  ): Promise<StandardizedBookingData> {
    try {
      // Validate and sanitize data
      const validation = this.validateBookingData(formData, serviceType, user);

      if (!validation.isValid) {
        const errorMessages = validation.errors.map(err => err.message).join(', ');
        throw new Error(`Validation failed: ${errorMessages}`);
      }

      if (!validation.sanitizedData) {
        throw new Error('Failed to sanitize booking data');
      }

      const bookingData = validation.sanitizedData;

      ErrorHandler.logInfo('Saving booking to database', {
        serviceType,
        userId: user?.id,
        hasContact: !!bookingData.contact.email
      });

      // Transform data for database insertion - only include columns that exist
      const dbData: any = {
        user_id: bookingData.user_id,
        service_type: bookingData.service_type,
        property_details: bookingData.property_details,
        service_details: bookingData.service_details,
        schedule: bookingData.schedule,
        contact: bookingData.contact,
        status: bookingData.status,
        total_price: bookingData.total_price,
        special_instructions: bookingData.special_instructions,
        metadata: bookingData.metadata,
        payment_status: bookingData.payment_status || 'pending'
      };

      ErrorHandler.logInfo('Inserting booking data to database', {
        serviceType: dbData.service_type,
        userId: dbData.user_id,
        hasContact: !!dbData.contact?.email,
        dataStructure: {
          property_details: Object.keys(dbData.property_details || {}),
          service_details: Object.keys(dbData.service_details || {}),
          schedule: Object.keys(dbData.schedule || {}),
          contact: Object.keys(dbData.contact || {})
        },
        dbDataKeys: Object.keys(dbData),
        sampleData: {
          service_type: dbData.service_type,
          status: dbData.status,
          user_id: dbData.user_id,
          hasPaymentStatus: !!dbData.payment_status
        }
      });

      // Save to database using Supabase - simplified query without columns parameter
      const { data, error } = await supabase
        .from('booking_forms')
        .insert([dbData])
        .select()
        .single();

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError(
          'DATABASE_ERROR',
          'Failed to save booking to database',
          error
        ));

        // Log detailed error information
        console.error('Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code,
          dbData: dbData
        });

        // Provide more specific error messages based on error type
        if (error.code === 'PGRST204') {
          throw new Error('Database schema error: Missing required columns. Please contact support.');
        } else if (error.code === '23505') {
          throw new Error('A booking with this information already exists. Please check your previous bookings.');
        } else if (error.code === '23503') {
          throw new Error('Invalid user reference. Please log in again and try again.');
        } else if (error.code === '23514') {
          throw new Error('Invalid data format. Please check your form data and try again.');
        } else if (error.message.includes('JWT')) {
          throw new Error('Authentication expired. Please log in again.');
        } else if (error.message.includes('permission')) {
          throw new Error('Permission denied. Please log in again.');
        } else if (error.message.includes('network')) {
          throw new Error('Network connection issue. Please check your internet connection and try again.');
        }

        throw new Error(`Failed to save booking: ${error.message || 'Database error'}`);
      }

      if (!data) {
        throw new Error('No data returned from booking save operation');
      }

      ErrorHandler.logSuccess('Booking saved successfully', {
        bookingId: data.id,
        serviceType: bookingData.service_type,
        userId: user?.id
      });

      return data as StandardizedBookingData;
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_SAVE_ERROR',
        'Failed to save booking',
        error
      ));
      throw error;
    }
  }

  /**
   * Update booking status
   */
  static async updateBookingStatus(
    bookingId: string,
    status: StandardizedBookingData['status'],
    paymentStatus?: StandardizedBookingData['payment_status']
  ): Promise<void> {
    try {
      const updateData: Partial<StandardizedBookingData> = {
        status,
        updated_at: new Date().toISOString()
      };

      if (paymentStatus) {
        updateData.payment_status = paymentStatus;
      }

      const { error } = await supabase
        .from('booking_forms')
        .update(updateData)
        .eq('id', bookingId);

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError(
          'DATABASE_ERROR',
          'Failed to update booking status',
          error
        ));
        throw new Error('Failed to update booking status');
      }

      ErrorHandler.logInfo('Booking status updated', {
        bookingId,
        status,
        paymentStatus
      });
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_UPDATE_ERROR',
        'Failed to update booking status',
        error
      ));
      throw error;
    }
  }

  /**
   * Get user bookings
   */
  static async getUserBookings(userId: string): Promise<StandardizedBookingData[]> {
    try {
      const { data, error } = await supabase
        .from('booking_forms')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        ErrorHandler.logError(ErrorHandler.createError(
          'DATABASE_ERROR',
          'Failed to fetch user bookings',
          error
        ));
        throw new Error('Failed to fetch bookings');
      }

      return data as StandardizedBookingData[];
    } catch (error) {
      ErrorHandler.logError(ErrorHandler.createError(
        'BOOKING_FETCH_ERROR',
        'Failed to fetch user bookings',
        error
      ));
      throw error;
    }
    }

  /**
   * Helper to get field value from formData, supporting both flat and nested structures
   */
  private static getFieldValue(formData: any, fieldName: string): any {
    // Check flat structure first
    if (formData && formData[fieldName] !== undefined) {
      return formData[fieldName];
    }
    
    // Check contact nested structure
    if (formData && formData.contact && formData.contact[fieldName] !== undefined) {
      return formData.contact[fieldName];
    }
    
    // Check propertyDetails nested structure
    if (formData && formData.propertyDetails && formData.propertyDetails[fieldName] !== undefined) {
      return formData.propertyDetails[fieldName];
    }
    
    // Check property_details nested structure
    if (formData && formData.property_details) {
      // Handle property type mapping
      if (fieldName === 'propertyType' && formData.property_details.type !== undefined) {
        return formData.property_details.type;
      }
      if (formData.property_details[fieldName] !== undefined) {
        return formData.property_details[fieldName];
      }
    }
    
    // Check schedule nested structure
    if (formData && formData.schedule && formData.schedule[fieldName] !== undefined) {
      return formData.schedule[fieldName];
    }
    
    return undefined;
  }
}