import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, CheckCircle, ExternalLink, AlertCircle, Loader, 
  CreditCard, DollarSign, Smartphone, Building2,
  Shield, Star, Clock, ArrowRight, ArrowLeft
} from 'lucide-react';
import { Button } from './ui/Button';
import { User } from '@supabase/supabase-js';
import { EnhancedPaymentService } from '../lib/api/enhancedPaymentService';
import { BookingService } from '../lib/api/bookingService';
import { MoveOutBookingService } from '../lib/api/moveOutBookingService';
import { SquarePaymentModal } from './SquarePaymentModal';
import type { PaymentResult } from '../lib/square';

interface PaymentOptionsModalProps {
  isOpen: boolean;
  onClose: () => void;
  amount: number;
  description?: string;
  customerEmail?: string;
  formData: Record<string, any>;
  user: User | null;
  onPaymentComplete?: () => void;
}

type PaymentMethod = 'square' | 'paypal' | 'cashapp' | 'zelle';

interface PaymentOption {
  id: PaymentMethod;
  name: string;
  icon: React.ReactNode;
  description: string;
  processingTime: string;
  fees: string;
  available: boolean;
  color: string;
  bgColor: string;
}

export function PaymentOptionsModal({ 
  isOpen, 
  onClose, 
  amount, 
  description,
  customerEmail,
  formData,
  user,
  onPaymentComplete
}: PaymentOptionsModalProps) {
  // Debug logging for props
  console.log('PaymentOptionsModal props received:', {
    isOpen,
    amount,
    description,
    customerEmail,
    formDataServiceType: formData?.serviceType,
    formDataMoveType: formData?.moveType,
    formDataPropertyType: formData?.propertyType,
    hasUser: !!user,
    fullFormData: formData
  });
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null);
  const [currentStep, setCurrentStep] = useState<'select' | 'process' | 'success'>('select');
  const [paymentLink, setPaymentLink] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeModal, setActiveModal] = useState<'options' | 'square'>('options');
  // Remove showOptions and showSquareModal states
  // const [showOptions, setShowOptions] = useState(true);
  // const [showSquareModal, setShowSquareModal] = useState(false);
  const [squarePaymentData, setSquarePaymentData] = useState<{
    amount: number;
    description: string;
    customerEmail: string;
    orderId: string;
    paymentRecordId: string;
    bookingId: string;
  } | null>(null);

  const paymentOptions: PaymentOption[] = [
    {
      id: 'square',
      name: 'Credit/Debit Card',
      icon: <CreditCard className="w-6 h-6" />,
      description: 'Secure payment with any major credit or debit card',
      processingTime: 'Instant',
      fees: 'No additional fees',
      available: true,
      color: 'text-green-400',
      bgColor: 'bg-green-500/20 border-green-400/30'
    },
    {
      id: 'paypal',
      name: 'PayPal',
      icon: (
        <svg className="w-6 h-6" viewBox="0 0 24 24" fill="currentColor">
          <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.254-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.563.563 0 0 0-.556.479l-1.187 7.527h-.506l-.24 1.516a.641.641 0 0 0 .633.74h3.94a.563.563 0 0 0 .556-.479l.035-.22.671-4.25.043-.28a.563.563 0 0 1 .556-.479h.35c3.863 0 6.884-1.567 7.76-6.101.368-1.892.177-3.472-.803-4.417z"/>
        </svg>
      ),
      description: 'Pay with your PayPal account or linked cards',
      processingTime: 'Instant',
      fees: 'No additional fees',
      available: false,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/20 border-blue-400/30'
    },
    {
      id: 'cashapp',
      name: 'Cash App',
      icon: <Smartphone className="w-6 h-6" />,
      description: 'Quick payment through Cash App',
      processingTime: 'Instant',
      fees: 'No additional fees',
      available: false,
      color: 'text-emerald-400',
      bgColor: 'bg-emerald-500/20 border-emerald-400/30'
    },
    {
      id: 'zelle',
      name: 'Zelle',
      icon: <Building2 className="w-6 h-6" />,
      description: 'Bank-to-bank transfer via Zelle',
      processingTime: 'Within minutes',
      fees: 'No fees',
      available: false,
      color: 'text-purple-400',
      bgColor: 'bg-purple-500/20 border-purple-400/30'
    }
  ];

  const handleMethodSelect = (method: PaymentMethod) => {
    setSelectedMethod(method);
    setError(null);
  };

  const handleProceedToPayment = async () => {
    if (!selectedMethod) return;

    if (selectedMethod === 'square') {
      await processSquarePayment();
    } else {
      setCurrentStep('process');
      setTimeout(() => {
        setError(`${paymentOptions.find(p => p.id === selectedMethod)?.name} integration coming soon! Please use Credit/Debit Card for now.`);
        setCurrentStep('select');
      }, 2000);
    }
  };

  const processSquarePayment = async () => {
    try {
      setIsLoading(true);
      setCurrentStep('process');
      setError(null);

      console.log("🔧 Processing Square payment - TIMESTAMP:", new Date().toISOString());

      // Validate required data before processing
      if (!formData || !user) {
        throw new Error('Missing required data for booking creation');
      }

      // Validate user authentication
      if (!user.id) {
        throw new Error('User authentication required');
      }

      // Validate amount
      if (!amount || amount <= 0) {
        throw new Error('Invalid payment amount');
      }

      // First, create or get the booking
      let booking;
    try {
      console.log('Form data in processSquarePayment:', {
        hasExistingBooking: !!formData.existingBooking,
        bookingId: formData.bookingId,
        serviceType: formData.serviceType,
        moveType: formData.moveType,
        propertyType: formData.propertyType,
        fullFormData: formData
      });
      if (formData.existingBooking && formData.bookingId) {
        booking = formData.existingBooking;
        console.log('Using existing booking:', booking.id);
      } else {
        // Debug the exact formData received
        console.log('=== PAYMENT MODAL DEBUG ===');
        console.log('Received formData:', formData);
        console.log('formData.serviceType:', formData.serviceType);
        console.log('formData.cleaningType:', formData.cleaningType);
        console.log('formData.moveType:', formData.moveType);
        console.log('formData.propertyType:', formData.propertyType);
        console.log('All formData keys:', Object.keys(formData));

        // COMPLETELY OVERRIDE SERVICE TYPE FOR MOVE-OUT FORMS
        let serviceType = 'residential_move';

        // Also override the formData.serviceType to ensure consistency
        const correctedFormData = {
          ...formData,
          serviceType: 'residential_move',
          cleaningType: 'move'
        };

        console.log('FORCED serviceType to residential_move for debugging');
        console.log('Original formData.serviceType:', formData.serviceType);
        console.log('Corrected formData.serviceType:', correctedFormData.serviceType);

        console.log('Determined serviceType:', serviceType);
        console.log('Will use MoveOutBookingService:', serviceType === 'residential_move');
        console.log('========================');
        console.log('Creating booking with data:', {
          serviceType,
          userId: user.id,
          hasFormData: !!formData,
          hasMoveType: !!formData.moveType,
          willUseMoveOutService: serviceType === 'residential_move'
        });
        
        // TEMPORARILY USE BOOKINGSERVICE FOR ALL TYPES TO DEBUG
        console.log('Creating booking via BookingService with service type:', serviceType);
        console.log('Using corrected form data:', correctedFormData);
        booking = await BookingService.saveBooking(correctedFormData, serviceType, user);
      }
    } catch (bookingError) {
      console.error('Booking handling failed:', bookingError);
      throw bookingError;
    }

      // Validate booking
      if (!booking || !booking.id) {
        throw new Error('Invalid booking');
      }

      // Proceed with payment creation
      console.log('Creating payment for booking:', booking.id);

      const amountInCents = Math.round(amount * 100);

      const paymentResult = await EnhancedPaymentService.createPaymentForBooking(
        booking,
        amountInCents,
        user
      );

      if (paymentResult.success) {
        console.log('Payment created:', paymentResult);

        if (paymentResult.paymentUrl === 'SQUARE_WEB_SDK') {
          console.log("Using Square Web SDK for payment processing");

          const trackingData = {
            paymentRecordId: paymentResult.paymentRecordId,
            paymentLinkId: paymentResult.paymentLinkId,
            bookingId: booking.id,
            startTime: Date.now(),
            isActive: true,
            amount: amount,
            serviceType: booking.service_type
          };

          localStorage.setItem('paymentTracking', JSON.stringify(trackingData));

          setSquarePaymentData({
            amount: amountInCents,
            description: `${booking.service_type} - Service`,
            customerEmail: booking.contact.email || '',
            orderId: paymentResult.paymentLinkId,
            paymentRecordId: paymentResult.paymentRecordId,
            bookingId: booking.id
          });

          setActiveModal('square');
        } else {
          console.log("Redirecting to external payment page:", paymentResult.paymentUrl);
          window.location.href = paymentResult.paymentUrl;
        }
      } else {
        throw new Error(paymentResult.error || 'Failed to create payment record');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Payment processing failed';
      console.error("Square payment error:", errorMessage);
      setError(errorMessage);
      setCurrentStep('select');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRedirectToPayment = () => {
    if (paymentLink) {
      window.open(paymentLink, '_blank');
    }
  };

  const handleClose = () => {
    setSelectedMethod(null);
    setCurrentStep('select');
    setPaymentLink(null);
    setError(null);
    setIsLoading(false);
    onClose();
  };

  const handleBackToSelect = () => {
    setCurrentStep('select');
    setError(null);
  };

  // Square Payment Modal handlers
  const handleSquarePaymentSuccess = (result: PaymentResult) => {
    console.log('Square payment successful:', result);
    setSquarePaymentData(null);
    setActiveModal('options');
    // Show success message or redirect
    if (window.location.pathname !== '/thank-you') {
      window.location.href = '/thank-you?payment=success';
    }
  };

  const handleSquarePaymentError = (error: string) => {
    console.error('Square payment failed:', error);
    setSquarePaymentData(null);
    setActiveModal('options');
    setError(error);
    setCurrentStep('select');
  };

  const handleSquareModalClose = () => {
    setSquarePaymentData(null);
    setActiveModal('options');
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex min-h-screen items-center justify-center p-2 sm:p-4 lg:p-6">
            {/* Glassmorphism Backdrop */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/60 backdrop-blur-xl"
              onClick={handleClose}
            />

            {/* Modern Glassmorphism Modal */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              transition={{ type: "spring", duration: 0.5 }}
              className="relative w-full max-w-sm sm:max-w-md lg:max-w-lg bg-white/10 backdrop-blur-3xl border border-white/20 rounded-2xl sm:rounded-3xl shadow-2xl shadow-black/50 overflow-hidden"
            >
              {/* Ultra-clear liquid glass overlay */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/[0.08] via-white/[0.02] to-transparent pointer-events-none" />
              <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/[0.01] to-white/[0.04] pointer-events-none" />

              {/* Close Button */}
              <button
                onClick={handleClose}
                className="absolute top-4 right-4 sm:top-6 sm:right-6 p-2 sm:p-2 rounded-xl bg-white/10 backdrop-blur-xl border border-white/20 hover:bg-white/20 transition-all duration-300 z-10 group touch-manipulation"
              >
                <X className="w-4 h-4 sm:w-5 sm:h-5 text-white group-hover:text-gray-200" />
              </button>

              {/* Content */}
              <div className="p-4 sm:p-6 lg:p-8 relative z-10">
                <AnimatePresence mode="wait">
                  {currentStep === 'select' && (
                    <motion.div
                      key="select"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Header */}
                      <div className="text-center mb-6 sm:mb-8">
                        <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl sm:rounded-2xl flex items-center justify-center mx-auto mb-3 sm:mb-4 shadow-lg shadow-green-500/25">
                          <DollarSign className="w-6 h-6 sm:w-8 sm:h-8 text-white" />
                        </div>
                        <h2 className="text-xl sm:text-2xl font-bold text-white mb-2">
                          Choose Payment Method
                        </h2>
                        <p className="text-sm sm:text-base text-gray-300">
                          Secure payment for your cleaning service
                        </p>
                      </div>

                      {/* Amount Display */}
                      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 shadow-lg">
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                          <div>
                            <p className="text-xs sm:text-sm text-gray-300 mb-1">Total Amount</p>
                            <p className="text-2xl sm:text-3xl font-bold text-white">${amount.toFixed(2)}</p>
                          </div>
                          <div className="flex sm:flex-col sm:text-right space-x-4 sm:space-x-0 sm:space-y-2">
                            <div className="flex items-center text-green-400 text-xs sm:text-sm font-medium">
                              <Shield className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                              Secure Payment
                            </div>
                            <div className="flex items-center text-yellow-400 text-xs sm:text-sm">
                              <Star className="w-3 h-3 sm:w-4 sm:h-4 mr-1 fill-current" />
                              SSL Encrypted
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Payment Options */}
                      <div className="space-y-3 sm:space-y-4 mb-6 sm:mb-8">
                        {paymentOptions.map((option) => (
                          <motion.button
                            key={option.id}
                            onClick={() => handleMethodSelect(option.id)}
                            disabled={!option.available}
                            className={`w-full p-3 sm:p-4 rounded-xl sm:rounded-2xl border-2 transition-all duration-300 relative overflow-hidden touch-manipulation ${
                              selectedMethod === option.id
                                ? `${option.bgColor} border-green-400 shadow-lg shadow-green-500/25`
                                : option.available
                                ? 'bg-white/5 border-white/20 hover:bg-white/10 hover:border-white/30 hover:shadow-lg'
                                : 'bg-white/5 border-white/10 opacity-50 cursor-not-allowed'
                            }`}
                            whileHover={option.available ? { scale: 1.02 } : {}}
                            whileTap={option.available ? { scale: 0.98 } : {}}
                          >
                            {/* Glass effect overlay for selected state */}
                            {selectedMethod === option.id && (
                              <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 via-emerald-500/5 to-green-500/10 pointer-events-none" />
                            )}
                            
                            <div className="flex items-center justify-between relative z-10">
                              <div className="flex items-center space-x-3 sm:space-x-4 flex-1">
                                <div className={`p-2 sm:p-3 rounded-lg sm:rounded-xl backdrop-blur-xl border border-white/20 ${
                                  selectedMethod === option.id ? 'bg-green-500/20 border-green-400/30' : 'bg-white/10'
                                } ${option.color} flex-shrink-0`}>
                                  {option.icon}
                                </div>
                                <div className="text-left flex-1 min-w-0">
                                  <div className="flex items-center gap-2 flex-wrap">
                                    <h3 className="font-semibold text-white text-sm sm:text-base">{option.name}</h3>
                                    {!option.available && (
                                      <span className="text-xs bg-white/20 text-gray-300 px-2 py-1 rounded-full backdrop-blur-xl whitespace-nowrap">
                                        Coming Soon
                                      </span>
                                    )}
                                  </div>
                                  <p className="text-xs sm:text-sm text-gray-300 leading-tight">{option.description}</p>
                                  <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 mt-1">
                                    <span className="text-xs text-green-400 font-medium">
                                      <Clock className="w-3 h-3 inline mr-1" />
                                      {option.processingTime}
                                    </span>
                                    <span className="text-xs text-gray-400">{option.fees}</span>
                                  </div>
                                </div>
                              </div>
                              {selectedMethod === option.id && (
                                <motion.div
                                  initial={{ scale: 0 }}
                                  animate={{ scale: 1 }}
                                  className="w-5 h-5 sm:w-6 sm:h-6 bg-green-500 rounded-full flex items-center justify-center shadow-lg flex-shrink-0"
                                >
                                  <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
                                </motion.div>
                              )}
                            </div>
                          </motion.button>
                        ))}
                      </div>

                      {/* Error Display */}
                      {error && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mb-6 p-4 bg-red-500/20 border border-red-400/30 rounded-xl flex items-start backdrop-blur-xl"
                        >
                          <AlertCircle className="w-5 h-5 text-red-400 mr-3 flex-shrink-0 mt-0.5" />
                          <p className="text-red-300 text-sm">{error}</p>
                        </motion.div>
                      )}

                      {/* Continue Button */}
                      <Button
                        onClick={handleProceedToPayment}
                        disabled={!selectedMethod || isLoading}
                        className="w-full h-12 sm:h-14 text-base sm:text-lg font-semibold bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed touch-manipulation"
                      >
                        {isLoading ? (
                          <>
                            <Loader className="w-5 h-5 mr-2 animate-spin" />
                            Processing...
                          </>
                        ) : (
                          <>
                            Continue to Payment
                            <ArrowRight className="ml-2 w-5 h-5" />
                          </>
                        )}
                      </Button>
                    </motion.div>
                  )}

                  {currentStep === 'process' && (
                    <motion.div
                      key="process"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="text-center py-8 sm:py-12"
                    >
                      <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-500/20 backdrop-blur-xl border border-green-400/30 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                        <Loader className="w-8 h-8 sm:w-10 sm:h-10 text-green-400 animate-spin" />
                      </div>
                      <h3 className="text-lg sm:text-xl font-semibold text-white mb-3 sm:mb-4">
                        Processing Payment
                      </h3>
                      <p className="text-sm sm:text-base text-gray-300 mb-6 sm:mb-8 px-2">
                        Please wait while we set up your secure payment...
                      </p>
                      <Button
                        variant="outline"
                        onClick={handleBackToSelect}
                        className="flex items-center bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 touch-manipulation"
                      >
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Back to Payment Options
                      </Button>
                    </motion.div>
                  )}

                  {currentStep === 'success' && (
                    <motion.div
                      key="success"
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.3 }}
                      className="text-center py-6 sm:py-8"
                    >
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                        className="w-16 h-16 sm:w-20 sm:h-20 bg-green-500/20 backdrop-blur-xl border border-green-400/30 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6"
                      >
                        <CheckCircle className="w-8 h-8 sm:w-10 sm:h-10 text-green-400" />
                      </motion.div>
                      <h3 className="text-xl sm:text-2xl font-semibold text-white mb-3 sm:mb-4">
                        Redirecting to Payment...
                      </h3>
                      <p className="text-sm sm:text-base text-gray-300 mb-6 sm:mb-8 px-2">
                        You're being redirected to Square's secure payment page. If the page doesn't open automatically, click below.
                      </p>
                      <div className="space-y-4">
                        <Button 
                          onClick={handleRedirectToPayment} 
                          className="w-full h-12 sm:h-14 text-base sm:text-lg font-semibold bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg rounded-xl transition-all duration-300 transform hover:scale-105 touch-manipulation"
                        >
                          Open Payment Page
                          <ExternalLink className="ml-2 w-4 h-4 sm:w-5 sm:h-5" />
                        </Button>
                        <Button
                          variant="outline"
                          onClick={handleBackToSelect}
                          className="w-full bg-white/10 backdrop-blur-xl border border-white/20 text-white hover:bg-white/20 touch-manipulation"
                        >
                          Choose Different Method
                        </Button>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>

              {/* Trust Indicators with Glassmorphism */}
              <div className="bg-white/5 backdrop-blur-xl border-t border-white/20 px-4 sm:px-8 py-3 sm:py-4">
                <div className="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6 text-xs sm:text-sm text-gray-300">
                  <div className="flex items-center">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-green-400" />
                    <span>256-bit SSL</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-blue-400" />
                    <span>PCI Compliant</span>
                  </div>
                  <div className="flex items-center">
                    <Star className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 text-yellow-400 fill-current" />
                    <span className="hidden sm:inline">Trusted by 2,847+ customers</span>
                    <span className="sm:hidden">2,847+ customers</span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      )}

      {/* Square Payment Modal */}
      {squarePaymentData && (
        <SquarePaymentModal
          isOpen={true}
          onClose={handleSquareModalClose}
          amount={squarePaymentData.amount}
          description={squarePaymentData.description}
          customerEmail={squarePaymentData.customerEmail}
          orderId={squarePaymentData.orderId}
          paymentRecordId={squarePaymentData.paymentRecordId}
          bookingId={squarePaymentData.bookingId}
          onPaymentSuccess={handleSquarePaymentSuccess}
          onPaymentError={handleSquarePaymentError}
        />
      )}
    </AnimatePresence>
  );