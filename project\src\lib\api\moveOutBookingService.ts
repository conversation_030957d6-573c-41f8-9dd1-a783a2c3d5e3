/**
 * Move-Out Booking Management Service
 * 
 * This service replicates the complete backend infrastructure used by regular cleaning forms
 * specifically optimized for move-out/move-in cleaning services.
 * 
 * CRITICAL: This addresses the missing backend processing that caused move-out bookings to fail.
 */

import { supabase, isSupabaseConfigured } from '../supabase/client';
import { ServiceTypeStandardizer } from '../services/serviceTypeStandardizer';
import { BookingService } from './bookingService';
import type { User } from '@supabase/supabase-js';

export interface MoveOutFormData {
  // Property details
  propertySize: string;
  moveType: 'move-out' | 'move-in';
  propertyType: string;
  bedrooms: number;
  bathrooms: number;
  frequency: string;
  cleaningType: string;
  
  // Contact information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  
  // Service details
  addOns: string[];
  preferredDate: string;
  preferredTime: string;
  specialInstructions?: string;
  
  // Marketing
  howDidYouHear?: string;
  newsletter: boolean;
  
  // Calculated fields
  totalPrice?: number;
  serviceType?: string;
}

export interface MoveOutBookingData {
  user_id: string | null;
  service_type: 'residential_move';
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled';
  
  contact: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  
  property_details: {
    size: string;
    address: string;
    city: string;
    zipCode: string;
    moveType: string;
    propertyType: string;
    bedrooms: number;
    bathrooms: number;
  };
  
  service_details: {
    moveType: string;
    addOns: string[];
    totalPrice: number;
    specialInstructions?: string;
    submittedAt: string;
    source: 'move_out_booking_service';
    frequency: string;
    cleaningType: string;
  };
  
  schedule: {
    preferredDate: string;
    preferredTime: string;
  };
  
  total_price: number;
  metadata: {
    serviceDisplayName: string;
    howDidYouHear?: string;
    newsletter: boolean;
  };
}

export class MoveOutBookingService {
  
  /**
   * Validate move-out form data
   */
  static validateMoveOutData(formData: MoveOutFormData): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    // Required fields validation
    if (!formData.propertySize) errors.push('Property size is required');
    if (!formData.moveType) errors.push('Move type is required');
    if (!formData.propertyType) errors.push('Property type is required');
    if (!formData.bedrooms) errors.push('Number of bedrooms is required');
    if (!formData.bathrooms) errors.push('Number of bathrooms is required');
    if (!formData.frequency) errors.push('Frequency is required');
    if (!formData.cleaningType) errors.push('Cleaning type is required');
    if (!formData.firstName) errors.push('First name is required');
    if (!formData.lastName) errors.push('Last name is required');
    if (!formData.email) errors.push('Email is required');
    if (!formData.phone) errors.push('Phone number is required');
    if (!formData.address) errors.push('Address is required');
    if (!formData.city) errors.push('City is required');
    if (!formData.zipCode) errors.push('ZIP code is required');
    if (!formData.preferredDate) errors.push('Preferred date is required');
    if (!formData.preferredTime) errors.push('Preferred time is required');
    
    // Format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      errors.push('Invalid email format');
    }
    
    const phoneRegex = /^\d{10,}$/;
    if (formData.phone && !phoneRegex.test(formData.phone.replace(/\D/g, ''))) {
      errors.push('Invalid phone number format');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }
  
  /**
   * Transform move-out form data to standardized booking data
   */
  static transformToBookingData(formData: MoveOutFormData, user: User | null): MoveOutBookingData {
    return {
      user_id: user?.id || null,
      service_type: 'residential_move',
      status: 'pending',
      
      contact: {
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        phone: formData.phone
      },
      
      property_details: {
        size: formData.propertySize,
        address: formData.address,
        city: formData.city,
        zipCode: formData.zipCode,
        moveType: formData.moveType,
        propertyType: formData.propertyType,
        bedrooms: formData.bedrooms,
        bathrooms: formData.bathrooms
      },
      
      service_details: {
        moveType: formData.moveType,
        addOns: formData.addOns || [],
        totalPrice: formData.totalPrice || 0,
        specialInstructions: formData.specialInstructions || '',
        submittedAt: new Date().toISOString(),
        source: 'move_out_booking_service',
        frequency: formData.frequency,
        cleaningType: formData.cleaningType
      },
      
      schedule: {
        preferredDate: formData.preferredDate,
        preferredTime: formData.preferredTime
      },
      
      total_price: formData.totalPrice || 0,
      
      metadata: {
        serviceDisplayName: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 'Move-In Cleaning',
        howDidYouHear: formData.howDidYouHear,
        newsletter: formData.newsletter
      }
    };
  }
  
  /**
   * Create move-out booking in database using the same infrastructure as regular cleaning
   */
  static async createMoveOutBooking(formData: MoveOutFormData, user: User | null): Promise<MoveOutBookingData> {
    try {
      if (!isSupabaseConfigured || !supabase) {
        throw new Error('Database connection not available');
      }
      
      if (!user) {
        throw new Error('User must be authenticated');
      }
      
      // Validate form data
      const validation = this.validateMoveOutData(formData);
      if (!validation.isValid) {
        throw new Error(`Validation failed: ${validation.errors.join(', ')}`);
      }
      
      console.log('=== MOVE-OUT BOOKING SERVICE DEBUG ===');
      console.log('Original formData:', formData);
      console.log('Setting serviceType to residential_move');

      // Standardize the form data using the existing infrastructure
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: 'residential_move'
      });

      console.log('Standardized formData:', standardizedFormData);
      console.log('Standardized serviceType:', standardizedFormData.serviceType);

      // Transform to booking data
      const bookingData = this.transformToBookingData(standardizedFormData, user);

      console.log('Creating move-out booking:', bookingData);
      console.log('Calling BookingService.saveBooking with serviceType: residential_move');

      // Use the existing BookingService infrastructure
      const savedBooking = await BookingService.saveBooking(
        standardizedFormData,
        'residential_move',
        user
      );
      
      console.log('Move-out booking created successfully:', savedBooking);
      
      return bookingData;
      
    } catch (error) {
      console.error('Error creating move-out booking:', error);
      throw new Error(`Failed to create move-out booking: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Process move-out payment completion using existing payment infrastructure
   */
  static async processMoveOutPaymentCompletion(formData: MoveOutFormData, user: User | null): Promise<{
    bookingId: string;
    redirectPath: string;
    successMessage: string;
  }> {
    try {
      // Create the booking first
      const bookingData = await this.createMoveOutBooking(formData, user);
      
      // Generate success data for frontend
      return {
        bookingId: bookingData.user_id || `MOVE-${Date.now()}`,
        redirectPath: '/accountdashboard',
        successMessage: `Your ${formData.moveType === 'move-out' ? 'move-out' : 'move-in'} cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
      };
      
    } catch (error) {
      console.error('Error processing move-out payment completion:', error);
      throw error;
    }
  }
  
  /**
   * Get move-out service pricing (can be enhanced later)
   */
  static calculateMoveOutPrice(propertySize: string, addOns: string[] = []): number {
    const basePrices: Record<string, number> = {
      'studio': 179,
      '2br': 249,
      '3br': 329,
      '4br+': 429
    };
    
    const addOnPrices: Record<string, number> = {
      'inside-appliances': 89,
      'inside-cabinets': 79,
      'garage-basement': 69,
      'window-cleaning': 99
    };
    
    const basePrice = basePrices[propertySize] || 179;
    const addOnTotal = addOns.reduce((total, addOn) => {
      return total + (addOnPrices[addOn] || 0);
    }, 0);
    
    return basePrice + addOnTotal;
  }
}

export default MoveOutBookingService;