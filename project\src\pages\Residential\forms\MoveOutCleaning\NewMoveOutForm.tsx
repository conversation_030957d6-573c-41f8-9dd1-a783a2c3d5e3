import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { 
  CheckCircle, ArrowRight, ChevronLeft, Home, Calendar, Clock,
  Users, Sparkles, Shield, Star
} from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { PaymentOptionsModal } from '../../../../components/PaymentOptionsModal';
import { useAuth } from '../../../../lib/auth/AuthProvider';
import { calculatePrice, type PricingInput } from '../../../../lib/services/pricingService';
import { ServiceTypeStandardizer } from '../../../../lib/services/serviceTypeStandardizer';
import { BookingService } from '../../../../lib/api/bookingService';

interface FormData {
  propertyType: string;
  propertySize: string;
  bedrooms: string;
  bathrooms: string;
  moveType: string;
  frequency: string;
  cleaningType: string;
  addOns: string[];
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  zipCode: string;
  preferredDate: string;
  preferredTime: string;
  specialInstructions: string;
}

interface ValidationErrors {
  [key: string]: string;
}

const NewMoveOutForm: React.FC = () => {
  console.log('🔧 NEW MOVE-OUT FORM COMPONENT LOADED - TIMESTAMP:', new Date().toISOString());

  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>({});

  const [formData, setFormData] = useState<FormData>({
    propertyType: '',
    propertySize: '',
    bedrooms: '',
    bathrooms: '',
    moveType: '',
    frequency: 'one-time',
    cleaningType: 'move',  // This should trigger residential_move
    addOns: [],
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    zipCode: '',
    preferredDate: '',
    preferredTime: '',
    specialInstructions: ''
  });

  // Debug: Log form data changes
  React.useEffect(() => {
    console.log('Form data updated:', formData);
  }, [formData]);

  const steps = [
    { id: 0, name: 'Property Details', description: 'Tell us about your property' },
    { id: 1, name: 'Move Details', description: 'Move-in or move-out service' },
    { id: 2, name: 'Add-Ons', description: 'Optional additional services' },
    { id: 3, name: 'Contact Info', description: 'Your contact information' }
  ];

  const addOnOptions = [
    { id: 'appliances', name: 'Inside Appliances', price: 40, description: 'Oven, fridge, microwave deep clean' },
    { id: 'cabinets', name: 'Inside Cabinets', price: 60, description: 'All kitchen and bathroom cabinets' },
    { id: 'garage', name: 'Garage Cleaning', price: 80, description: 'Sweep and organize garage space' },
    { id: 'windows', name: 'Window Cleaning', price: 50, description: 'Interior and exterior windows' },
    { id: 'carpet', name: 'Carpet Deep Clean', price: 100, description: 'Professional carpet cleaning' },
    { id: 'pressure-wash', name: 'Exterior Pressure Wash', price: 120, description: 'Deck, patio, and walkways' }
  ];

  // Calculate total price using the same logic as working forms
  const calculateTotalPrice = (): number => {
    try {
      // Determine service type based on cleaning type (same as regular form)
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Convert property size to square footage estimate (same as regular form)
      const sizeToSqft: Record<string, number> = {
        'studio': 500,
        'small': 800,
        'medium': 1200,
        'large': 1800,
        'xlarge': 2500
      };

      // Prepare pricing input (same structure as regular form)
      const pricingInput: PricingInput = {
        serviceType: serviceType,
        propertySize: sizeToSqft[formData.propertySize || 'medium'] || 1200,
        frequency: formData.frequency === 'bi-weekly' ? 'biweekly' :
                  formData.frequency === 'one-time' ? 'onetime' :
                  formData.frequency || 'onetime',
        addOns: formData.addOns || [],
        customOptions: {
          bedrooms: parseInt(formData.bedrooms || '0') || 0,
          bathrooms: parseInt(formData.bathrooms || '0') || 0,
          propertyType: formData.propertyType || 'house'
        }
      };

      // Calculate price using centralized service (same as regular form)
      const pricingResult = calculatePrice(pricingInput);
      return Math.round(pricingResult.total);
    } catch (error) {
      console.error('Error calculating price:', error);
      return 299; // Fallback price
    }
  };

  // Validation functions (same as regular form)
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const validatePhone = (phone: string): boolean => {
    const phoneRegex = /^\d{10,}$/;
    return phoneRegex.test(phone.replace(/\D/g, ''));
  };

  const validateZipCode = (zip: string): boolean => {
    const zipRegex = /^\d{5}(-\d{4})?$/;
    return zipRegex.test(zip);
  };

  const validateField = (field: string, value: string): string => {
    switch (field) {
      case 'firstName':
      case 'lastName':
        return value.length < 2 ? 'Must be at least 2 characters' : '';
      case 'email':
        return !validateEmail(value) ? 'Please enter a valid email address' : '';
      case 'phone':
        return !validatePhone(value) ? 'Please enter a valid 10-digit phone number' : '';
      case 'address':
        return value.length < 5 ? 'Please enter a complete address' : '';
      case 'city':
        return value.length < 2 ? 'Please enter a valid city' : '';
      case 'zipCode':
        return !validateZipCode(value) ? 'Please enter a valid ZIP code (12345 or 12345-6789)' : '';
      default:
        return '';
    }
  };

  // Step validation (same pattern as regular form)
  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 0:
        return !!(formData.propertyType && formData.propertySize && formData.bedrooms && formData.bathrooms);
      case 1:
        return !!(formData.moveType);
      case 2:
        return true; // Add-ons are optional
      case 3: {
        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'address', 'city', 'zipCode', 'preferredDate', 'preferredTime'];
        return requiredFields.every(field => {
          const value = formData[field as keyof FormData] as string;
          return value && !validateField(field, value);
        });
      }
      default:
        return false;
    }
  };

  const handleFieldChange = (field: string, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  // Form submission (same pattern as regular form)
  const handleSubmit = async () => {
    if (!isStepValid(3)) return;
    
    setIsSubmitting(true);
    
    try {
      if (!user) {
        // Save form data and redirect to login (same as regular form)
        localStorage.setItem('moveOutCleaningFormData', JSON.stringify(formData));
        navigate('/auth/login', { 
          state: { from: '/residential/moveout' }
        });
        return;
      }

      // Determine correct service type based on cleaning type (same as regular form)
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Standardize the form data before saving (same as regular form)
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save to localStorage for persistence (same as regular form)
      localStorage.setItem('residentialBookingData', JSON.stringify(standardizedFormData));
      
      // Show payment modal instead of navigating directly (same as regular form)
      setShowPaymentModal(true);
    } catch (error) {
      console.error('Submission error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle successful payment (same pattern as regular form)
  const handlePaymentComplete = async () => {
    setShowPaymentModal(false);
    
    try {
      // Determine correct service type based on cleaning type (same as regular form)
      let serviceType = 'residential_regular';
      if (formData.cleaningType === 'deep') {
        serviceType = 'residential_deep';
      } else if (formData.cleaningType === 'move') {
        serviceType = 'residential_move';
      }

      // Standardize the form data before saving (same as regular form)
      const standardizedFormData = ServiceTypeStandardizer.standardizeFormServiceType({
        ...formData,
        serviceType: serviceType,
        totalPrice: calculateTotalPrice(),
        submittedAt: new Date().toISOString()
      });

      // Save booking using the same service as regular form
      const savedBooking = await BookingService.saveBooking(
        standardizedFormData,
        serviceType,
        user
      );

      console.log('Booking saved successfully:', savedBooking);

      // Clear localStorage since booking is now saved (same as regular form)
      localStorage.removeItem('residentialBookingData');
      
      // Navigate to account dashboard with success state (same as regular form)
      navigate('/accountdashboard', { 
        state: { 
          newBooking: {
            id: savedBooking.id,
            type: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 'Move-In Cleaning',
            serviceType: formData.moveType === 'move-out' ? 'Move-Out Cleaning' : 'Move-In Cleaning',
            ...formData,
            totalPrice: calculateTotalPrice(),
            status: 'confirmed',
            message: `Your ${formData.moveType} cleaning service has been booked successfully! You'll receive a confirmation email shortly.`
          },
          showBookingsTab: true
        }
      });
    } catch (error) {
      console.error('Error completing booking:', error);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <motion.div key="step0" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Property Details</h2>
              <p className="text-white/70">Tell us about your property</p>
            </div>
            <div className="space-y-6">
              <div>
                <label className="block text-white font-medium mb-4">Property Type</label>
                <div className="grid grid-cols-2 gap-4">
                  {['house', 'apartment', 'condo', 'townhouse'].map(type => (
                    <motion.div
                      key={type}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-4 rounded-xl border-2 cursor-pointer ${
                        formData.propertyType === type
                          ? 'border-blue-400 bg-blue-500/20'
                          : 'border-white/20 bg-white/5'
                      }`}
                      onClick={() => handleFieldChange('propertyType', type)}
                    >
                      <h3 className="font-bold text-white text-center">{type.charAt(0).toUpperCase() + type.slice(1)}</h3>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-white font-medium mb-4">Property Size</label>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { id: 'studio', name: 'Studio/1BR', desc: 'Up to 600 sq ft' },
                    { id: 'small', name: '2 Bedroom', desc: '600-1000 sq ft' },
                    { id: 'medium', name: '3 Bedroom', desc: '1000-1500 sq ft' },
                    { id: 'large', name: '4+ Bedroom', desc: '1500+ sq ft' }
                  ].map(size => (
                    <motion.div
                      key={size.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-4 rounded-xl border-2 cursor-pointer ${
                        formData.propertySize === size.id
                          ? 'border-blue-400 bg-blue-500/20'
                          : 'border-white/20 bg-white/5'
                      }`}
                      onClick={() => handleFieldChange('propertySize', size.id)}
                    >
                      <h3 className="font-bold text-white">{size.name}</h3>
                      <p className="text-white/70 text-sm">{size.desc}</p>
                    </motion.div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-white font-medium mb-4">Bedrooms</label>
                  <select
                    value={formData.bedrooms}
                    onChange={e => handleFieldChange('bedrooms', e.target.value)}
                    className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  >
                    <option value="">Select</option>
                    {['1', '2', '3', '4', '5+'].map(b => <option key={b} value={b}>{b}</option>)}
                  </select>
                </div>
                <div>
                  <label className="block text-white font-medium mb-4">Bathrooms</label>
                  <select
                    value={formData.bathrooms}
                    onChange={e => handleFieldChange('bathrooms', e.target.value)}
                    className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                  >
                    <option value="">Select</option>
                    {['1', '1.5', '2', '2.5', '3', '3.5', '4+'].map(b => <option key={b} value={b}>{b}</option>)}
                  </select>
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Move Details</h2>
              <p className="text-white/70">What type of move cleaning do you need?</p>
            </div>
            <div className="space-y-6">
              <div>
                <label className="block text-white font-medium mb-4">Move Type</label>
                <div className="grid grid-cols-1 gap-4">
                  {[
                    { id: 'move-out', name: 'Move-Out Cleaning', desc: 'Deep clean before leaving your current property' },
                    { id: 'move-in', name: 'Move-In Cleaning', desc: 'Deep clean before moving into your new property' },
                    { id: 'both', name: 'Both Properties', desc: 'Clean both your old and new properties' }
                  ].map(type => (
                    <motion.div
                      key={type.id}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      className={`p-6 rounded-xl border-2 cursor-pointer ${
                        formData.moveType === type.id
                          ? 'border-blue-400 bg-blue-500/20'
                          : 'border-white/20 bg-white/5'
                      }`}
                      onClick={() => handleFieldChange('moveType', type.id)}
                    >
                      <h3 className="font-bold text-white text-lg mb-2">{type.name}</h3>
                      <p className="text-white/70">{type.desc}</p>
                    </motion.div>
                  ))}
                </div>
              </div>
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Add-On Services</h2>
              <p className="text-white/70">Enhance your cleaning service (optional)</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {addOnOptions.map(addon => (
                <motion.div
                  key={addon.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  className={`p-4 rounded-xl border-2 cursor-pointer ${
                    formData.addOns.includes(addon.id)
                      ? 'border-blue-400 bg-blue-500/20'
                      : 'border-white/20 bg-white/5'
                  }`}
                  onClick={() => {
                    const currentAddOns = formData.addOns || [];
                    if (currentAddOns.includes(addon.id)) {
                      handleFieldChange('addOns', currentAddOns.filter(id => id !== addon.id));
                    } else {
                      handleFieldChange('addOns', [...currentAddOns, addon.id]);
                    }
                  }}
                >
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="font-bold text-white">{addon.name}</h3>
                    <span className="text-blue-400 font-bold">+${addon.price}</span>
                  </div>
                  <p className="text-white/70 text-sm">{addon.description}</p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        );

      case 3:
        return (
          <motion.div key="step3" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} exit={{ opacity: 0, x: -50 }} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-3xl font-bold text-white mb-4">Contact Information</h2>
              <p className="text-white/70">Let us know how to reach you</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white font-medium mb-2">First Name</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={e => handleFieldChange('firstName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter first name"
                />
                {validationErrors.firstName && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.firstName}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Last Name</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={e => handleFieldChange('lastName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter last name"
                />
                {validationErrors.lastName && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.lastName}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Email</label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={e => handleFieldChange('email', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter email address"
                />
                {validationErrors.email && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.email}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Phone</label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={e => handleFieldChange('phone', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter phone number"
                />
                {validationErrors.phone && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.phone}</p>
                )}
              </div>
              <div className="md:col-span-2">
                <label className="block text-white font-medium mb-2">Address</label>
                <input
                  type="text"
                  value={formData.address}
                  onChange={e => handleFieldChange('address', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter property address"
                />
                {validationErrors.address && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.address}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">City</label>
                <input
                  type="text"
                  value={formData.city}
                  onChange={e => handleFieldChange('city', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter city"
                />
                {validationErrors.city && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.city}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">ZIP Code</label>
                <input
                  type="text"
                  value={formData.zipCode}
                  onChange={e => handleFieldChange('zipCode', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Enter ZIP code"
                />
                {validationErrors.zipCode && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.zipCode}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Preferred Date</label>
                <input
                  type="date"
                  value={formData.preferredDate}
                  onChange={e => handleFieldChange('preferredDate', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                />
                {validationErrors.preferredDate && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.preferredDate}</p>
                )}
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Preferred Time</label>
                <select
                  value={formData.preferredTime}
                  onChange={e => handleFieldChange('preferredTime', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white"
                >
                  <option value="">Select time</option>
                  <option value="morning">Morning (8AM - 12PM)</option>
                  <option value="afternoon">Afternoon (12PM - 5PM)</option>
                  <option value="evening">Evening (5PM - 8PM)</option>
                </select>
                {validationErrors.preferredTime && (
                  <p className="text-red-400 text-sm mt-1">{validationErrors.preferredTime}</p>
                )}
              </div>
              <div className="md:col-span-2">
                <label className="block text-white font-medium mb-2">Special Instructions</label>
                <textarea
                  value={formData.specialInstructions}
                  onChange={e => handleFieldChange('specialInstructions', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Any special instructions or requests..."
                  rows={3}
                />
              </div>
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      <AnimatedBackground>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <motion.h1
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-4xl md:text-5xl font-bold text-white mb-4"
              >
                🔧 NEW Move-Out/In Cleaning (DEBUG VERSION)
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-xl text-white/80"
              >
                Professional deep cleaning for your move
              </motion.p>
            </div>

            {/* Progress Bar */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-4">
                {steps.map((step, index) => (
                  <div key={step.id} className="flex items-center">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      index <= currentStep ? 'bg-blue-500 text-white' : 'bg-white/20 text-white/60'
                    }`}>
                      {index < currentStep ? <CheckCircle className="w-5 h-5" /> : index + 1}
                    </div>
                    {index < steps.length - 1 && (
                      <div className={`w-16 h-1 mx-2 ${
                        index < currentStep ? 'bg-blue-500' : 'bg-white/20'
                      }`} />
                    )}
                  </div>
                ))}
              </div>
              <div className="text-center">
                <h3 className="text-white font-semibold">{steps[currentStep].name}</h3>
                <p className="text-white/70 text-sm">{steps[currentStep].description}</p>
              </div>
            </div>

            {/* Form Content */}
            <div className="bg-white/10 backdrop-blur-xl rounded-2xl border border-white/20 p-8 mb-8">
              <AnimatePresence mode="wait">
                {renderStep()}
              </AnimatePresence>
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center">
              <div>
                {currentStep > 0 && (
                  <Button
                    onClick={prevStep}
                    variant="outline"
                    className="text-white border-white/20"
                  >
                    <ChevronLeft className="w-4 h-4 mr-2" />
                    Back
                  </Button>
                )}
              </div>

              <div className="text-center">
                <div className="text-white/70 text-sm mb-2">Estimated Total</div>
                <div className="text-3xl font-bold text-white">${calculateTotalPrice()}</div>
              </div>

              <div>
                {currentStep < steps.length - 1 ? (
                  <Button
                    onClick={nextStep}
                    disabled={!isStepValid(currentStep)}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={isSubmitting || !isStepValid(3)}
                    className="bg-green-500 hover:bg-green-600 text-white"
                  >
                    {isSubmitting ? 'Processing...' : 'Book Now'}
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </AnimatedBackground>

      {/* Payment Modal - Using exact same pattern as working forms */}
      <PaymentOptionsModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        amount={calculateTotalPrice()}
        description="Move-Out/In Cleaning Service"
        customerEmail={formData.email || ''}
        formData={(() => {
          console.log('=== MOVE-OUT FORM DEBUG ===');
          console.log('formData.cleaningType:', formData.cleaningType);
          console.log('formData.moveType:', formData.moveType);
          console.log('Raw formData:', formData);

          // Explicitly set service type to residential_move for move-out cleaning
          const paymentData = {
            ...formData,
            serviceType: 'residential_move',
            cleaningType: 'move',
            moveType: formData.moveType || 'move-out',
            totalPrice: calculateTotalPrice(),
            // Add explicit flags for PaymentOptionsModal detection
            isMove: true,
            isMoveOut: true
          };

          console.log('Payment data before standardization:', paymentData);

          // TEMPORARILY BYPASS STANDARDIZATION TO TEST
          console.log('Bypassing standardization, returning raw payment data');
          console.log('Final serviceType:', paymentData.serviceType);
          console.log('========================');

          return paymentData;
        })()}
        user={user}
        onPaymentComplete={handlePaymentComplete}
      />
    </div>
  );
};

export default NewMoveOutForm;
