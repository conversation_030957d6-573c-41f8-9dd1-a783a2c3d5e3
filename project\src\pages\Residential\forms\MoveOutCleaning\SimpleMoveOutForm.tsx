import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ArrowRight, CheckCircle } from 'lucide-react';
import { Button } from '../../../../components/ui/Button';
import { AnimatedBackground } from '../../../../components/layout/AnimatedBackground';
import { useAuth } from '../../../../lib/auth/AuthProvider';

interface FormData {
  propertyType: string;
  propertySize: string;
  moveType: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

const SimpleMoveOutForm: React.FC = () => {
  console.log('🔧 SIMPLE MOVE-OUT FORM LOADED - TIMESTAMP:', new Date().toISOString());

  const navigate = useNavigate();
  const { user } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);

  const [formData, setFormData] = useState<FormData>({
    propertyType: '',
    propertySize: '',
    moveType: '',
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  });

  const steps = [
    { id: 0, name: 'Property' },
    { id: 1, name: 'Move Type' },
    { id: 2, name: 'Contact' }
  ];

  const handleFieldChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const isStepValid = (step: number): boolean => {
    switch (step) {
      case 0:
        return !!(formData.propertyType && formData.propertySize);
      case 1:
        return !!(formData.moveType);
      case 2:
        return !!(formData.firstName && formData.lastName && formData.email && formData.phone);
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleSubmit = () => {
    console.log('Form submitted:', formData);
    alert('Form submitted successfully! (This is a test)');
  };

  const renderStep = () => {
    switch (currentStep) {
      case 0:
        return (
          <motion.div key="step0" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} className="space-y-6">
            <h2 className="text-2xl font-bold text-white mb-4">Property Details</h2>
            
            <div>
              <label className="block text-white font-medium mb-2">Property Type</label>
              <div className="grid grid-cols-2 gap-3">
                {['house', 'apartment', 'condo', 'townhouse'].map(type => (
                  <button
                    key={type}
                    onClick={() => handleFieldChange('propertyType', type)}
                    className={`p-3 rounded-lg border-2 ${
                      formData.propertyType === type
                        ? 'border-blue-400 bg-blue-500/20 text-white'
                        : 'border-white/20 bg-white/5 text-white/70'
                    }`}
                  >
                    {type.charAt(0).toUpperCase() + type.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            <div>
              <label className="block text-white font-medium mb-2">Property Size</label>
              <div className="grid grid-cols-2 gap-3">
                {[
                  { id: 'small', name: 'Small (1-2 BR)' },
                  { id: 'medium', name: 'Medium (3 BR)' },
                  { id: 'large', name: 'Large (4+ BR)' },
                  { id: 'studio', name: 'Studio' }
                ].map(size => (
                  <button
                    key={size.id}
                    onClick={() => handleFieldChange('propertySize', size.id)}
                    className={`p-3 rounded-lg border-2 ${
                      formData.propertySize === size.id
                        ? 'border-blue-400 bg-blue-500/20 text-white'
                        : 'border-white/20 bg-white/5 text-white/70'
                    }`}
                  >
                    {size.name}
                  </button>
                ))}
              </div>
            </div>
          </motion.div>
        );

      case 1:
        return (
          <motion.div key="step1" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} className="space-y-6">
            <h2 className="text-2xl font-bold text-white mb-4">Move Type</h2>
            
            <div className="space-y-3">
              {[
                { id: 'move-out', name: 'Move-Out Cleaning', desc: 'Deep clean before leaving' },
                { id: 'move-in', name: 'Move-In Cleaning', desc: 'Deep clean before moving in' },
                { id: 'both', name: 'Both Services', desc: 'Complete move cleaning package' }
              ].map(type => (
                <button
                  key={type.id}
                  onClick={() => handleFieldChange('moveType', type.id)}
                  className={`w-full p-4 rounded-lg border-2 text-left ${
                    formData.moveType === type.id
                      ? 'border-blue-400 bg-blue-500/20'
                      : 'border-white/20 bg-white/5'
                  }`}
                >
                  <div className="text-white font-medium">{type.name}</div>
                  <div className="text-white/70 text-sm">{type.desc}</div>
                </button>
              ))}
            </div>
          </motion.div>
        );

      case 2:
        return (
          <motion.div key="step2" initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} className="space-y-6">
            <h2 className="text-2xl font-bold text-white mb-4">Contact Information</h2>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-white font-medium mb-2">First Name</label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={e => handleFieldChange('firstName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="John"
                />
              </div>
              <div>
                <label className="block text-white font-medium mb-2">Last Name</label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={e => handleFieldChange('lastName', e.target.value)}
                  className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                  placeholder="Doe"
                />
              </div>
            </div>

            <div>
              <label className="block text-white font-medium mb-2">Email</label>
              <input
                type="email"
                value={formData.email}
                onChange={e => handleFieldChange('email', e.target.value)}
                className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                placeholder="<EMAIL>"
              />
            </div>

            <div>
              <label className="block text-white font-medium mb-2">Phone</label>
              <input
                type="tel"
                value={formData.phone}
                onChange={e => handleFieldChange('phone', e.target.value)}
                className="w-full p-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/50"
                placeholder="(*************"
              />
            </div>
          </motion.div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen">
      <AnimatedBackground>
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-2xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-white mb-4">
                Simple Move-Out Form
              </h1>
              <p className="text-white/80">
                Simplified version for testing
              </p>
            </div>

            {/* Progress */}
            <div className="flex justify-center mb-8">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                    index <= currentStep ? 'bg-blue-500 text-white' : 'bg-white/20 text-white/60'
                  }`}>
                    {index < currentStep ? <CheckCircle className="w-5 h-5" /> : index + 1}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-12 h-1 mx-2 ${
                      index < currentStep ? 'bg-blue-500' : 'bg-white/20'
                    }`} />
                  )}
                </div>
              ))}
            </div>

            {/* Form Content */}
            <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/20">
              {renderStep()}

              {/* Navigation */}
              <div className="flex justify-end mt-8">
                {currentStep < steps.length - 1 ? (
                  <Button
                    onClick={nextStep}
                    disabled={!isStepValid(currentStep)}
                    className="bg-blue-500 hover:bg-blue-600 text-white"
                  >
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    onClick={handleSubmit}
                    disabled={!isStepValid(2)}
                    className="bg-green-500 hover:bg-green-600 text-white"
                  >
                    Submit Test
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </div>
      </AnimatedBackground>
    </div>
  );
};

export default SimpleMoveOutForm;
